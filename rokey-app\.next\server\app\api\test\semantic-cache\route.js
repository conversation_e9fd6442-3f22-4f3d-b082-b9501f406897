/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/test/semantic-cache/route";
exports.ids = ["app/api/test/semantic-cache/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_test_semantic_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/test/semantic-cache/route.ts */ \"(rsc)/./src/app/api/test/semantic-cache/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/test/semantic-cache/route\",\n        pathname: \"/api/test/semantic-cache\",\n        filename: \"route\",\n        bundlePath: \"app/api/test/semantic-cache/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\test\\\\semantic-cache\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_test_semantic_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/test/semantic-cache/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/test/semantic-cache/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/semantic-cache/SemanticCacheService */ \"(rsc)/./src/lib/semantic-cache/SemanticCacheService.ts\");\n/* harmony import */ var _lib_semantic_cache_tierUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/semantic-cache/tierUtils */ \"(rsc)/./src/lib/semantic-cache/tierUtils.ts\");\n/**\n * Test endpoint for Semantic Cache functionality\n * This endpoint allows testing the semantic cache without going through the full chat completions flow\n */ \n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, userId, configId, promptText, responseData } = body;\n        if (!userId || !configId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'userId and configId are required'\n            }, {\n                status: 400\n            });\n        }\n        const userTier = await (0,_lib_semantic_cache_tierUtils__WEBPACK_IMPORTED_MODULE_2__.getUserTier)(userId);\n        if (action === 'search') {\n            if (!promptText) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'promptText is required for search'\n                }, {\n                    status: 400\n                });\n            }\n            console.log(`[Semantic Cache Test] Searching cache for user ${userId} (tier: ${userTier})`);\n            const cacheHit = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.searchCache({\n                promptText,\n                modelUsed: 'test-model',\n                providerUsed: 'test-provider',\n                temperature: 0.7,\n                maxTokens: 1000,\n                metadata: {\n                    test: true\n                }\n            }, userId, configId, userTier);\n            if (cacheHit) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    cacheHit: true,\n                    data: {\n                        similarity: cacheHit.similarity,\n                        promptText: cacheHit.promptText,\n                        responseData: cacheHit.responseData,\n                        hitCount: cacheHit.hitCount,\n                        createdAt: cacheHit.createdAt\n                    }\n                });\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    cacheHit: false,\n                    message: 'No cache hit found'\n                });\n            }\n        }\n        if (action === 'store') {\n            if (!promptText || !responseData) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'promptText and responseData are required for store'\n                }, {\n                    status: 400\n                });\n            }\n            console.log(`[Semantic Cache Test] Storing cache for user ${userId} (tier: ${userTier})`);\n            const stored = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.storeCache({\n                promptText,\n                modelUsed: 'test-model',\n                providerUsed: 'test-provider',\n                temperature: 0.7,\n                maxTokens: 1000,\n                metadata: {\n                    test: true\n                }\n            }, {\n                responseData,\n                tokensPrompt: 100,\n                tokensCompletion: 200,\n                cost: 0.001\n            }, userId, configId, userTier);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                stored,\n                message: stored ? 'Response stored in cache' : 'Failed to store in cache (tier restrictions or error)'\n            });\n        }\n        if (action === 'stats') {\n            console.log(`[Semantic Cache Test] Getting stats for user ${userId}`);\n            const stats = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.getCacheStats(userId, configId, 7);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                stats,\n                userTier\n            });\n        }\n        if (action === 'cleanup') {\n            console.log(`[Semantic Cache Test] Running cleanup`);\n            const deletedCount = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.cleanupExpiredCache();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                deletedCount,\n                message: `Cleaned up ${deletedCount} expired cache entries`\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid action. Use: search, store, stats, or cleanup'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('[Semantic Cache Test] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Semantic Cache Test Endpoint',\n        usage: {\n            'POST /api/test/semantic-cache': {\n                description: 'Test semantic cache functionality',\n                actions: {\n                    search: 'Search for cached responses',\n                    store: 'Store a response in cache',\n                    stats: 'Get cache statistics',\n                    cleanup: 'Clean up expired cache entries'\n                },\n                example: {\n                    action: 'search',\n                    userId: 'user-uuid',\n                    configId: 'config-uuid',\n                    promptText: 'Hello, how are you?'\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/test/semantic-cache/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/embeddings/jina.ts":
/*!************************************!*\
  !*** ./src/lib/embeddings/jina.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiKeyJinaEmbeddings: () => (/* binding */ MultiKeyJinaEmbeddings),\n/* harmony export */   jinaEmbeddings: () => (/* binding */ jinaEmbeddings)\n/* harmony export */ });\n/**\r\n * Multi-Key Jina Embeddings v3 Implementation\r\n * Provides automatic key rotation and high rate limits for RouKey\r\n */ class MultiKeyJinaEmbeddings {\n    constructor(){\n        this.currentKeyIndex = 0;\n        this.keyUsage = new Map();\n        this.baseUrl = 'https://api.jina.ai/v1/embeddings';\n        this.model = 'jina-embeddings-v3';\n        // Load all Jina API keys from environment\n        this.apiKeys = [\n            process.env.JINA_API_KEY,\n            process.env.JINA_API_KEY_2,\n            process.env.JINA_API_KEY_3,\n            process.env.JINA_API_KEY_4,\n            process.env.JINA_API_KEY_5,\n            process.env.JINA_API_KEY_6,\n            process.env.JINA_API_KEY_7,\n            process.env.JINA_API_KEY_9,\n            process.env.JINA_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Jina API keys found in environment variables');\n        }\n        console.log(`[Jina Embeddings] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage stats for each key\n        this.apiKeys.forEach((key)=>{\n            this.keyUsage.set(key, {\n                requests: 0,\n                tokens: 0,\n                lastUsed: new Date(),\n                errors: 0\n            });\n        });\n    }\n    /**\r\n   * Get the next API key using round-robin rotation\r\n   */ getNextKey() {\n        const key = this.apiKeys[this.currentKeyIndex];\n        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;\n        return key;\n    }\n    /**\r\n   * Get the best available API key based on usage and error rates\r\n   */ getBestKey() {\n        // For now, use simple round-robin\n        // TODO: Implement smart selection based on usage stats\n        return this.getNextKey();\n    }\n    /**\r\n   * Update usage statistics for a key\r\n   */ updateKeyUsage(apiKey, tokens, isError = false) {\n        const stats = this.keyUsage.get(apiKey);\n        if (stats) {\n            stats.requests++;\n            stats.tokens += tokens;\n            stats.lastUsed = new Date();\n            if (isError) {\n                stats.errors++;\n                stats.lastError = new Date();\n            }\n        }\n    }\n    /**\r\n   * Generate embedding for a single text input\r\n   */ async embedQuery(text) {\n        const maxRetries = this.apiKeys.length;\n        let lastError = null;\n        for(let attempt = 0; attempt < maxRetries; attempt++){\n            try {\n                const apiKey = this.getBestKey();\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1}/${maxRetries} with key ${this.apiKeys.indexOf(apiKey) + 1}`);\n                const response = await fetch(this.baseUrl, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        model: this.model,\n                        input: [\n                            text\n                        ],\n                        normalized: true,\n                        embedding_type: 'float'\n                    })\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    if (response.status === 429) {\n                        console.log(`[Jina Embeddings] Rate limit hit for key ${this.apiKeys.indexOf(apiKey) + 1}, trying next key...`);\n                        this.updateKeyUsage(apiKey, 0, true);\n                        continue;\n                    }\n                    throw new Error(`HTTP ${response.status}: ${errorText}`);\n                }\n                const data = await response.json();\n                if (!data.data || data.data.length === 0) {\n                    throw new Error('No embedding data returned from Jina API');\n                }\n                const embedding = data.data[0].embedding;\n                // Update usage stats\n                this.updateKeyUsage(apiKey, data.usage?.total_tokens || text.length);\n                console.log(`[Jina Embeddings] Success with key ${this.apiKeys.indexOf(apiKey) + 1} (${embedding.length} dimensions, ${data.usage?.total_tokens || 'unknown'} tokens)`);\n                return embedding;\n            } catch (error) {\n                lastError = error;\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1} failed:`, error.message);\n                // If this is the last attempt, throw the error\n                if (attempt === maxRetries - 1) {\n                    break;\n                }\n            }\n        }\n        // All keys failed\n        console.error(`[Jina Embeddings] All ${maxRetries} API keys failed`);\n        throw new Error(`All Jina API keys failed. Last error: ${lastError?.message || 'Unknown error'}`);\n    }\n    /**\r\n   * Generate embeddings for multiple texts (batch processing)\r\n   */ async embedDocuments(texts) {\n        // For now, process sequentially to avoid overwhelming the API\n        // TODO: Implement smart batching based on rate limits\n        const embeddings = [];\n        for(let i = 0; i < texts.length; i++){\n            console.log(`[Jina Embeddings] Processing document ${i + 1}/${texts.length}`);\n            const embedding = await this.embedQuery(texts[i]);\n            embeddings.push(embedding);\n            // Small delay to respect rate limits\n            if (i < texts.length - 1) {\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n        }\n        return embeddings;\n    }\n    /**\r\n   * Get usage statistics for all keys\r\n   */ getUsageStats() {\n        const stats = {};\n        this.apiKeys.forEach((key, index)=>{\n            const keyStats = this.keyUsage.get(key);\n            if (keyStats) {\n                stats[`key_${index + 1}`] = {\n                    ...keyStats\n                };\n            }\n        });\n        return stats;\n    }\n    /**\r\n   * Get total capacity across all keys\r\n   */ getTotalCapacity() {\n        return {\n            totalKeys: this.apiKeys.length,\n            estimatedRPM: this.apiKeys.length * 500,\n            estimatedTokensPerMonth: this.apiKeys.length * 1000000\n        };\n    }\n}\n// Export a singleton instance\nconst jinaEmbeddings = new MultiKeyJinaEmbeddings();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/embeddings/jina.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/semantic-cache/SemanticCacheService.ts":
/*!********************************************************!*\
  !*** ./src/lib/semantic-cache/SemanticCacheService.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SemanticCacheService: () => (/* binding */ SemanticCacheService),\n/* harmony export */   semanticCache: () => (/* binding */ semanticCache)\n/* harmony export */ });\n/* harmony import */ var _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/embeddings/jina */ \"(rsc)/./src/lib/embeddings/jina.ts\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * Semantic Cache Service for RouKey\n * Provides advanced caching using vector embeddings for semantic similarity\n */ \n\n\nclass SemanticCacheService {\n    constructor(){\n        // Cache configuration by tier\n        this.tierConfig = {\n            free: {\n                enabled: false,\n                ttlHours: 0,\n                similarityThreshold: 0.90,\n                maxCacheSize: 0\n            },\n            starter: {\n                enabled: false,\n                ttlHours: 0,\n                similarityThreshold: 0.90,\n                maxCacheSize: 0\n            },\n            pro: {\n                enabled: true,\n                ttlHours: 24,\n                similarityThreshold: 0.85,\n                maxCacheSize: 1000\n            },\n            enterprise: {\n                enabled: true,\n                ttlHours: 168,\n                similarityThreshold: 0.80,\n                maxCacheSize: 10000\n            }\n        };\n    }\n    static getInstance() {\n        if (!SemanticCacheService.instance) {\n            SemanticCacheService.instance = new SemanticCacheService();\n        }\n        return SemanticCacheService.instance;\n    }\n    /**\n   * Generate a hash for exact prompt matching\n   */ generatePromptHash(prompt, model, temperature) {\n        const hashInput = `${prompt}|${model}|${temperature || 0}`;\n        return crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash('sha256').update(hashInput).digest('hex');\n    }\n    /**\n   * Check if semantic caching is enabled for the given tier\n   */ isCacheEnabled(tier) {\n        return this.tierConfig[tier].enabled;\n    }\n    /**\n   * Get cache configuration for a tier\n   */ getTierConfig(tier) {\n        return this.tierConfig[tier];\n    }\n    /**\n   * Search for cached responses using semantic similarity\n   */ async searchCache(request, userId, configId, tier) {\n        try {\n            if (!this.isCacheEnabled(tier)) {\n                return null;\n            }\n            const config = this.getTierConfig(tier);\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n            // First, try exact hash match for perfect duplicates\n            const promptHash = this.generatePromptHash(request.promptText, request.modelUsed, request.temperature);\n            const { data: exactMatch, error: exactError } = await supabase.from('semantic_cache').select('*').eq('prompt_hash', promptHash).eq('user_id', userId).eq('custom_api_config_id', configId).gt('expires_at', new Date().toISOString()).limit(1).single();\n            if (exactMatch && !exactError) {\n                console.log('[Semantic Cache] Exact hash match found');\n                await this.incrementHitCount(exactMatch.id);\n                return this.formatCachedResult(exactMatch, 1.0);\n            }\n            // If no exact match, try semantic similarity search\n            const queryEmbedding = await _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_0__.jinaEmbeddings.embedQuery(request.promptText);\n            const { data: semanticMatches, error: semanticError } = await supabase.rpc('search_semantic_cache', {\n                query_embedding: queryEmbedding,\n                config_id: configId,\n                user_id_param: userId,\n                similarity_threshold: config.similarityThreshold,\n                match_count: 1\n            });\n            if (semanticError) {\n                console.error('[Semantic Cache] Search error:', semanticError);\n                return null;\n            }\n            if (semanticMatches && semanticMatches.length > 0) {\n                const match = semanticMatches[0];\n                console.log(`[Semantic Cache] Semantic match found with ${(match.similarity * 100).toFixed(1)}% similarity`);\n                await this.incrementHitCount(match.id);\n                return this.formatCachedResult(match, match.similarity);\n            }\n            console.log('[Semantic Cache] No cache match found');\n            return null;\n        } catch (error) {\n            console.error('[Semantic Cache] Search error:', error);\n            return null;\n        }\n    }\n    /**\n   * Store a response in the semantic cache\n   */ async storeCache(request, response, userId, configId, tier) {\n        try {\n            if (!this.isCacheEnabled(tier)) {\n                return false;\n            }\n            const config = this.getTierConfig(tier);\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n            // Generate embedding for the prompt\n            const promptEmbedding = await _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_0__.jinaEmbeddings.embedQuery(request.promptText);\n            const promptHash = this.generatePromptHash(request.promptText, request.modelUsed, request.temperature);\n            // Calculate expiration time\n            const expiresAt = new Date();\n            expiresAt.setHours(expiresAt.getHours() + config.ttlHours);\n            // Store in cache\n            const { error } = await supabase.from('semantic_cache').insert({\n                user_id: userId,\n                custom_api_config_id: configId,\n                prompt_text: request.promptText,\n                prompt_embedding: promptEmbedding,\n                prompt_hash: promptHash,\n                model_used: request.modelUsed,\n                provider_used: request.providerUsed,\n                temperature: request.temperature,\n                max_tokens: request.maxTokens,\n                request_metadata: request.metadata || {},\n                response_data: response.responseData,\n                response_tokens_prompt: response.tokensPrompt,\n                response_tokens_completion: response.tokensCompletion,\n                response_cost: response.cost,\n                cache_tier: tier,\n                expires_at: expiresAt.toISOString()\n            });\n            if (error) {\n                console.error('[Semantic Cache] Store error:', error);\n                return false;\n            }\n            console.log(`[Semantic Cache] Stored response for ${tier} tier (expires: ${expiresAt.toISOString()})`);\n            return true;\n        } catch (error) {\n            console.error('[Semantic Cache] Store error:', error);\n            return false;\n        }\n    }\n    /**\n   * Get cache statistics for a user/config\n   */ async getCacheStats(userId, configId, days = 7) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n            const startDate = new Date();\n            startDate.setDate(startDate.getDate() - days);\n            const { data, error } = await supabase.from('semantic_cache_analytics').select('*').eq('user_id', userId).eq('custom_api_config_id', configId).gte('date', startDate.toISOString().split('T')[0]);\n            if (error) {\n                console.error('[Semantic Cache] Stats error:', error);\n                return this.getEmptyStats();\n            }\n            if (!data || data.length === 0) {\n                return this.getEmptyStats();\n            }\n            // Aggregate statistics\n            const totals = data.reduce((acc, row)=>({\n                    totalRequests: acc.totalRequests + (row.total_requests || 0),\n                    cacheHits: acc.cacheHits + (row.cache_hits || 0),\n                    cacheMisses: acc.cacheMisses + (row.cache_misses || 0),\n                    tokensSaved: acc.tokensSaved + (row.tokens_saved || 0),\n                    costSaved: acc.costSaved + (row.cost_saved || 0),\n                    responseTimeSum: acc.responseTimeSum + (row.avg_response_time_ms || 0),\n                    rowCount: acc.rowCount + 1\n                }), {\n                totalRequests: 0,\n                cacheHits: 0,\n                cacheMisses: 0,\n                tokensSaved: 0,\n                costSaved: 0,\n                responseTimeSum: 0,\n                rowCount: 0\n            });\n            return {\n                totalRequests: totals.totalRequests,\n                cacheHits: totals.cacheHits,\n                cacheMisses: totals.cacheMisses,\n                hitRate: totals.totalRequests > 0 ? totals.cacheHits / totals.totalRequests : 0,\n                tokensSaved: totals.tokensSaved,\n                costSaved: totals.costSaved,\n                avgResponseTime: totals.rowCount > 0 ? totals.responseTimeSum / totals.rowCount : 0\n            };\n        } catch (error) {\n            console.error('[Semantic Cache] Stats error:', error);\n            return this.getEmptyStats();\n        }\n    }\n    /**\n   * Clean up expired cache entries\n   */ async cleanupExpiredCache() {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n            const { data, error } = await supabase.rpc('cleanup_semantic_cache');\n            if (error) {\n                console.error('[Semantic Cache] Cleanup error:', error);\n                return 0;\n            }\n            console.log(`[Semantic Cache] Cleaned up ${data} expired entries`);\n            return data || 0;\n        } catch (error) {\n            console.error('[Semantic Cache] Cleanup error:', error);\n            return 0;\n        }\n    }\n    /**\n   * Private helper methods\n   */ async incrementHitCount(cacheId) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n            await supabase.rpc('increment_cache_hit', {\n                cache_id: cacheId\n            });\n        } catch (error) {\n            console.error('[Semantic Cache] Hit count increment error:', error);\n        }\n    }\n    formatCachedResult(cacheEntry, similarity) {\n        return {\n            id: cacheEntry.id,\n            promptText: cacheEntry.prompt_text,\n            responseData: cacheEntry.response_data,\n            modelUsed: cacheEntry.model_used,\n            providerUsed: cacheEntry.provider_used,\n            similarity,\n            hitCount: cacheEntry.hit_count,\n            createdAt: cacheEntry.created_at,\n            expiresAt: cacheEntry.expires_at\n        };\n    }\n    getEmptyStats() {\n        return {\n            totalRequests: 0,\n            cacheHits: 0,\n            cacheMisses: 0,\n            hitRate: 0,\n            tokensSaved: 0,\n            costSaved: 0,\n            avgResponseTime: 0\n        };\n    }\n}\n// Export singleton instance\nconst semanticCache = SemanticCacheService.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/semantic-cache/SemanticCacheService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/semantic-cache/tierUtils.ts":
/*!*********************************************!*\
  !*** ./src/lib/semantic-cache/tierUtils.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canStoreMoreCacheEntries: () => (/* binding */ canStoreMoreCacheEntries),\n/* harmony export */   getCacheLimits: () => (/* binding */ getCacheLimits),\n/* harmony export */   getTierDisplayInfo: () => (/* binding */ getTierDisplayInfo),\n/* harmony export */   getUserTier: () => (/* binding */ getUserTier),\n/* harmony export */   getUserTierInfo: () => (/* binding */ getUserTierInfo),\n/* harmony export */   hasSemanticCacheAccess: () => (/* binding */ hasSemanticCacheAccess),\n/* harmony export */   logTierEvent: () => (/* binding */ logTierEvent)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/**\n * Tier Utilities for Semantic Caching\n * Determines user subscription tier and cache permissions\n */ \n/**\n * Get user's subscription tier from subscriptions table\n */ async function getUserTier(userId) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createSupabaseServerClient)();\n        // Get user's subscription information from subscriptions table\n        const { data: subscription, error } = await supabase.from('subscriptions').select('tier, status').eq('user_id', userId).eq('status', 'active').single();\n        if (error || !subscription) {\n            console.log('[Tier Utils] No active subscription found, defaulting to free tier');\n            return 'free';\n        }\n        // Map subscription tier to cache tier\n        const subscriptionTierToCache = {\n            'starter': 'starter',\n            'professional': 'pro',\n            'enterprise': 'enterprise'\n        };\n        const tier = subscriptionTierToCache[subscription.tier] || 'free';\n        console.log(`[Tier Utils] User ${userId} has tier: ${tier} (subscription_tier: ${subscription.tier})`);\n        return tier;\n    } catch (error) {\n        console.error('[Tier Utils] Error getting user tier:', error);\n        return 'free';\n    }\n}\n/**\n * Get comprehensive tier information including cache settings\n */ async function getUserTierInfo(userId) {\n    const tier = await getUserTier(userId);\n    const tierConfigs = {\n        free: {\n            tier: 'free',\n            semanticCacheEnabled: false,\n            maxCacheEntries: 0,\n            cacheTTLHours: 0\n        },\n        starter: {\n            tier: 'starter',\n            semanticCacheEnabled: false,\n            maxCacheEntries: 0,\n            cacheTTLHours: 0\n        },\n        pro: {\n            tier: 'pro',\n            semanticCacheEnabled: true,\n            maxCacheEntries: 1000,\n            cacheTTLHours: 24 // 1 day\n        },\n        enterprise: {\n            tier: 'enterprise',\n            semanticCacheEnabled: true,\n            maxCacheEntries: 10000,\n            cacheTTLHours: 168 // 1 week\n        }\n    };\n    return tierConfigs[tier];\n}\n/**\n * Check if user has access to semantic caching\n */ async function hasSemanticCacheAccess(userId) {\n    const tierInfo = await getUserTierInfo(userId);\n    return tierInfo.semanticCacheEnabled;\n}\n/**\n * Get cache limits for a user\n */ async function getCacheLimits(userId) {\n    const tierInfo = await getUserTierInfo(userId);\n    return {\n        maxEntries: tierInfo.maxCacheEntries,\n        ttlHours: tierInfo.cacheTTLHours,\n        enabled: tierInfo.semanticCacheEnabled\n    };\n}\n/**\n * Validate if user can store more cache entries\n */ async function canStoreMoreCacheEntries(userId, configId) {\n    try {\n        const tierInfo = await getUserTierInfo(userId);\n        if (!tierInfo.semanticCacheEnabled) {\n            return false;\n        }\n        // If unlimited (enterprise with very high limit), allow\n        if (tierInfo.maxCacheEntries >= 10000) {\n            return true;\n        }\n        // Check current cache entry count\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createSupabaseServerClient)();\n        const { count, error } = await supabase.from('semantic_cache').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId).eq('custom_api_config_id', configId).gt('expires_at', new Date().toISOString());\n        if (error) {\n            console.error('[Tier Utils] Error checking cache count:', error);\n            return false;\n        }\n        return (count || 0) < tierInfo.maxCacheEntries;\n    } catch (error) {\n        console.error('[Tier Utils] Error validating cache storage:', error);\n        return false;\n    }\n}\n/**\n * Get tier display information for UI\n */ function getTierDisplayInfo(tier) {\n    const displayInfo = {\n        free: {\n            name: 'Free',\n            color: 'gray',\n            features: [\n                'Basic routing',\n                'Simple caching',\n                'Community support'\n            ]\n        },\n        starter: {\n            name: 'Starter',\n            color: 'blue',\n            features: [\n                'Limited role routing',\n                'Simple caching',\n                'Prompt engineering'\n            ]\n        },\n        pro: {\n            name: 'Professional',\n            color: 'orange',\n            features: [\n                'Semantic caching',\n                'Advanced routing',\n                'Knowledge base',\n                'Priority support'\n            ]\n        },\n        enterprise: {\n            name: 'Enterprise',\n            color: 'purple',\n            features: [\n                'Advanced semantic caching',\n                'Custom rules',\n                'Team management',\n                'Dedicated support'\n            ]\n        }\n    };\n    return displayInfo[tier];\n}\n/**\n * Log tier-related events for analytics\n */ async function logTierEvent(userId, event, metadata) {\n    try {\n        const tier = await getUserTier(userId);\n        console.log(`[Tier Analytics] User ${userId} (${tier}): ${event}`, metadata);\n    // Here you could store analytics in a separate table if needed\n    // For now, we'll just log to console\n    } catch (error) {\n        console.error('[Tier Utils] Error logging tier event:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/semantic-cache/tierUtils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tr46","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();